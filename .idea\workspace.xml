<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="NONE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="8d932bd6-24b5-400c-b89e-fa3d78b24c67" name="更改" comment="fix:修改聊天消息背景颜色以及圆角" />
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="ClangdSettings">
    <option name="formatViaClangd" value="false" />
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 1
}</component>
  <component name="ProjectId" id="2zXUtIkh0ZwdIMdBDYwrTeqja9r" />
  <component name="ProjectLevelVcsManager">
    <ConfirmationsSetting value="2" id="Add" />
  </component>
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent"><![CDATA[{
  "keyToString": {
    "Flutter.main.dart.executor": "Run",
    "RunOnceActivity.ShowReadmeOnStart": "true",
    "RunOnceActivity.cidr.known.project.marker": "true",
    "RunOnceActivity.git.unshallow": "true",
    "RunOnceActivity.readMode.enableVisualFormatting": "true",
    "cf.first.check.clang-format": "false",
    "cidr.known.project.marker": "true",
    "dart.analysis.tool.window.visible": "false",
    "git-widget-placeholder": "pre",
    "kotlin-language-version-configured": "true",
    "last_opened_file_path": "E:/zzf_code/chat-uikit-flutter/images/svg",
    "settings.editor.selected.configurable": "flutter.settings",
    "show.migrate.to.gradle.popup": "false"
  }
}]]></component>
  <component name="RecentsManager">
    <key name="CopyFile.RECENT_KEYS">
      <recent name="E:\zzf_code\chat-uikit-flutter\images\svg" />
      <recent name="E:\zzf_code\chat-uikit-flutter\lib" />
      <recent name="E:\zzf_code\chat-uikit-flutter\lib\utils" />
    </key>
  </component>
  <component name="RunManager">
    <configuration name="main.dart" type="FlutterRunConfigurationType" factoryName="Flutter">
      <option name="filePath" value="$PROJECT_DIR$/example/lib/main.dart" />
      <method v="2" />
    </configuration>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="应用程序级" UseSingleDictionary="true" transferred="true" />
  <component name="TaskManager">
    <task active="true" id="Default" summary="默认任务">
      <changelist id="8d932bd6-24b5-400c-b89e-fa3d78b24c67" name="更改" comment="" />
      <created>1751877124888</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1751877124888</updated>
    </task>
    <task id="LOCAL-00001" summary="fix:确认添加好友时的 loading 状态和跳转流程">
      <option name="closed" value="true" />
      <created>1752646767822</created>
      <option name="number" value="00001" />
      <option name="presentableId" value="LOCAL-00001" />
      <option name="project" value="LOCAL" />
      <updated>1752646767822</updated>
    </task>
    <task id="LOCAL-00002" summary="fix:">
      <option name="closed" value="true" />
      <created>1755499200233</created>
      <option name="number" value="00002" />
      <option name="presentableId" value="LOCAL-00002" />
      <option name="project" value="LOCAL" />
      <updated>1755499200233</updated>
    </task>
    <task id="LOCAL-00003" summary="fix:修改icon视频图标大小">
      <option name="closed" value="true" />
      <created>1755765891701</created>
      <option name="number" value="00003" />
      <option name="presentableId" value="LOCAL-00003" />
      <option name="project" value="LOCAL" />
      <updated>1755765891701</updated>
    </task>
    <task id="LOCAL-00004" summary="fix:隐藏表情包发送按钮">
      <option name="closed" value="true" />
      <created>1756780612170</created>
      <option name="number" value="00004" />
      <option name="presentableId" value="LOCAL-00004" />
      <option name="project" value="LOCAL" />
      <updated>1756780612170</updated>
    </task>
    <task id="LOCAL-00005" summary="fix:表情包背景颜色更换">
      <option name="closed" value="true" />
      <created>1756782218785</created>
      <option name="number" value="00005" />
      <option name="presentableId" value="LOCAL-00005" />
      <option name="project" value="LOCAL" />
      <updated>1756782218785</updated>
    </task>
    <task id="LOCAL-00006" summary="fix:表情包切换背景颜色更换">
      <option name="closed" value="true" />
      <created>1756782919205</created>
      <option name="number" value="00006" />
      <option name="presentableId" value="LOCAL-00006" />
      <option name="project" value="LOCAL" />
      <updated>1756782919205</updated>
    </task>
    <task id="LOCAL-00007" summary="fix:修改聊天消息背景颜色">
      <option name="closed" value="true" />
      <created>1756795137265</created>
      <option name="number" value="00007" />
      <option name="presentableId" value="LOCAL-00007" />
      <option name="project" value="LOCAL" />
      <updated>1756795137265</updated>
    </task>
    <task id="LOCAL-00008" summary="fix:修改聊天消息背景颜色">
      <option name="closed" value="true" />
      <created>1756798350825</created>
      <option name="number" value="00008" />
      <option name="presentableId" value="LOCAL-00008" />
      <option name="project" value="LOCAL" />
      <updated>1756798350825</updated>
    </task>
    <task id="LOCAL-00009" summary="fix:修改聊天消息背景颜色以及圆角">
      <option name="closed" value="true" />
      <created>1756804601883</created>
      <option name="number" value="00009" />
      <option name="presentableId" value="LOCAL-00009" />
      <option name="project" value="LOCAL" />
      <updated>1756804601883</updated>
    </task>
    <option name="localTasksCounter" value="10" />
    <servers />
  </component>
  <component name="Vcs.Log.Tabs.Properties">
    <option name="TAB_STATES">
      <map>
        <entry key="MAIN">
          <value>
            <State />
          </value>
        </entry>
      </map>
    </option>
  </component>
  <component name="VcsManagerConfiguration">
    <MESSAGE value="fix:确认添加好友时的 loading 状态和跳转流程" />
    <MESSAGE value="fix:" />
    <MESSAGE value="fix:修改icon视频图标大小" />
    <MESSAGE value="fix:隐藏表情包发送按钮" />
    <MESSAGE value="fix:表情包背景颜色更换" />
    <MESSAGE value="fix:表情包切换背景颜色更换" />
    <MESSAGE value="fix:修改聊天消息背景颜色" />
    <MESSAGE value="fix:修改聊天消息背景颜色以及圆角" />
    <option name="LAST_COMMIT_MESSAGE" value="fix:修改聊天消息背景颜色以及圆角" />
  </component>
</project>